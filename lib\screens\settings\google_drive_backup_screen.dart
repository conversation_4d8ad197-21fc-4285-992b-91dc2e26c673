/// 바라 부스 매니저 - 구글 드라이브 백업 화면
///
/// 구글 드라이브를 통한 데이터 백업 및 복원 기능을 제공하는 화면입니다.
/// - 구글 계정 연동
/// - 데이터 백업
/// - 백업 파일 목록 조회
/// - 데이터 복원
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import '../../services/google_drive_service.dart';
import '../../services/backup_service.dart';
import '../../services/database_service.dart';
import '../../providers/subscription_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/toast_utils.dart';
import '../../utils/logger_utils.dart';
import '../../widgets/app_bar_styles.dart';
import '../../widgets/skeleton_loading.dart';

/// 구글 드라이브 백업 화면
class GoogleDriveBackupScreen extends ConsumerStatefulWidget {
  const GoogleDriveBackupScreen({super.key});

  @override
  ConsumerState<GoogleDriveBackupScreen> createState() => _GoogleDriveBackupScreenState();
}

class _GoogleDriveBackupScreenState extends ConsumerState<GoogleDriveBackupScreen> {
  static const String _tag = 'GoogleDriveBackupScreen';

  final GoogleDriveService _driveService = GoogleDriveService();
  late final BackupService _backupService;

  bool _isAuthenticated = false;
  GoogleSignInAccount? _currentAccount;
  List<Map<String, dynamic>> _backupFiles = [];

  // 로딩 상태 분리(전체화면 로딩 제거)
  bool _authLoading = false;      // 인증/초기 상태 확인
  // bool _filesLoading = false;     // (미사용) 백업 파일 메타 로딩
  bool _backupBtnLoading = false; // 백업 덮어쓰기 버튼 로딩
  bool _restoreBtnLoading = false; // 최신 복원 버튼 로딩

  @override
  void initState() {
    super.initState();
    _backupService = BackupService(DatabaseServiceImpl());
    _checkAuthStatus();
  }

  /// 인증 상태 확인
  Future<void> _checkAuthStatus() async {
    setState(() => _authLoading = true);
    try {
      // 조용한 인증 먼저 시도
      final silent = await _driveService.authenticateSilently();
      _isAuthenticated = silent;
      if (_isAuthenticated) {
        _currentAccount = await _driveService.currentAccount;
        await _loadBackupFiles();
      }
    } catch (e) {
      LoggerUtils.logError('인증 상태 확인 실패', tag: _tag, error: e);
    } finally {
      setState(() { _authLoading = false; });
    }
  }

  /// 구글 계정 연동
  Future<void> _authenticateGoogle() async {
    // 플러스 플랜 확인
    final isPlusUser = await ref.read(isPlusUserProvider.future);
    if (!isPlusUser) {
      _showUpgradeDialog();
      return;
    }

    setState(() => _authLoading = true);

    try {
      // 버튼으로 진입한 경우에는 반드시 계정 선택 창을 띄운다
      final success = await _driveService.authenticateWithAccountPicker();
      if (success) {
        _isAuthenticated = true;
        _currentAccount = await _driveService.currentAccount;
        await _loadBackupFiles();
        if (mounted) ToastUtils.showSuccess(context, '구글 계정 연동이 완료되었습니다.');
      } else {
        if (mounted) ToastUtils.showError(context, '구글 계정 연동에 실패했습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('구글 인증 실패', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '구글 계정 연동 중 오류가 발생했습니다.');
      }
    } finally {
      setState(() { _authLoading = false; });
    }
  }

  /// 백업 파일 목록 로드
  Future<void> _loadBackupFiles() async {
    try {
      final files = await _driveService.getBackupFiles();
      setState(() => _backupFiles = files);
    } catch (e) {
      LoggerUtils.logError('백업 파일 목록 로드 실패', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '백업 파일 목록을 불러올 수 없습니다.');
      }
    }
  }

  /// 데이터 백업
  Future<void> _backupData() async {
    setState(() => _backupBtnLoading = true);
    try {
      final backupData = await _backupService.createBackup();
      final success = await _driveService.backupData(backupData.toJson());
      if (success) {
        await _loadBackupFiles();
        if (mounted) ToastUtils.showSuccess(context, '데이터 백업이 완료되었습니다.');
      } else {
        if (mounted) ToastUtils.showError(context, '데이터 백업에 실패했습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('데이터 백업 실패', tag: _tag, error: e);
      if (mounted) ToastUtils.showError(context, '백업 중 오류가 발생했습니다.');
    } finally {
      setState(() => _backupBtnLoading = false);
    }
  }

  /// 데이터 복원
  Future<void> _restoreData(String fileId, String fileName) async {
    final confirmed = await _showRestoreConfirmDialog(fileName);
    if (!confirmed) return;

    setState(() => _restoreBtnLoading = true);

    try {
      final data = await _driveService.restoreData(fileId);
      if (data == null) {
        throw Exception('백업 파일을 읽을 수 없습니다');
      }

      final backupData = BackupData.fromJson(data);
      final success = await _backupService.restoreBackup(backupData);

      if (success) {
        if (mounted) {
          ToastUtils.showSuccess(context, '데이터 복원이 완료되었습니다.\n앱을 재시작합니다.');
          // 잠시 후 앱 자동 재시작
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              Phoenix.rebirth(context);
            }
          });
        }
      } else {
        if (mounted) {
          ToastUtils.showError(context, '데이터 복원에 실패했습니다.');
        }
      }
    } catch (e) {
      LoggerUtils.logError('데이터 복원 실패', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '복원 중 오류가 발생했습니다.');
      }
    } finally {
      setState(() => _restoreBtnLoading = false);
    }
  }


  /// 구글 계정 연동 해제
  Future<void> _signOut() async {
    final confirmed = await _showSignOutConfirmDialog();
    if (!confirmed) return;

    setState(() => _authLoading = true);

    try {
      await _driveService.signOut();
      setState(() {
        _isAuthenticated = false;
        _currentAccount = null;
        _backupFiles.clear();
      });
      if (mounted) ToastUtils.showSuccess(context, '구글 계정 연동이 해제되었습니다.');
    } catch (e) {
      LoggerUtils.logError('구글 계정 연동 해제 실패', tag: _tag, error: e);
      if (mounted) ToastUtils.showError(context, '연동 해제 중 오류가 발생했습니다.');
    } finally {
      setState(() => _authLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx) => Text('클라우드로 백업', style: AppBarStyles.of(ctx))),
        centerTitle: true,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInfoCard(),
              const SizedBox(height: 24),
              _buildAuthSection(),
              if (_isAuthenticated) ...[
                const SizedBox(height: 24),
                _buildSimpleBackupCard(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 정보 카드
  Widget _buildInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.primarySeed),
                const SizedBox(width: 8),
                Text(
                  '클라우드 백업',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primarySeed,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '• 모든 행사, 상품, 판매 데이터를 안전하게 백업합니다\n'
              '• 백업 파일은 본인의 클라우드에 저장됩니다\n'
              '• 언제든지 데이터를 복원할 수 있습니다\n'
              '• 플러스 플랜 전용 기능입니다',
              style: TextStyle(
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 인증 섹션
  Widget _buildAuthSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('구글 계정 연동', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                if (_isAuthenticated && _currentAccount != null)
                  TextButton(
                    onPressed: _authLoading ? null : _signOut,
                    child: _authLoading
                        ? const SizedBox(height: 16, width: 16, child: CircularProgressIndicator(strokeWidth: 2))
                        : const Text('연동 해제'),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            if (_isAuthenticated && _currentAccount != null) ...[
              Row(
                children: [
                  CircleAvatar(
                    backgroundImage: _currentAccount!.photoUrl != null ? NetworkImage(_currentAccount!.photoUrl!) : null,
                    child: _currentAccount!.photoUrl == null ? Icon(Icons.person) : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _currentAccount!.displayName ?? '',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          _currentAccount!.email,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ] else ...[
              // 스켈레톤 또는 버튼
              if (_authLoading) ...[
                _buildAuthSectionSkeleton(),
              ] else ...[
                const Text('계정을 연동하여 백업 기능을 사용하세요.', style: TextStyle(fontSize: 14)),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _authenticateGoogle,
                    icon: const Icon(Icons.login),
                    label: const Text('권한 허용하기'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primarySeed,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  /// 간소화된 백업 카드 (마지막 백업 정보 + 덮어쓰기/복원)
  Widget _buildSimpleBackupCard() {
    // 마지막 백업 정보 준비
    String lastInfo = '백업 기록 없음';
    String sizeInfo = '';
    String? fileId;
    if (_backupFiles.isNotEmpty) {
      final file = _backupFiles.first;
      fileId = file['id'] as String?;
      final createdTime = file['createdTime'] as String?;
      final size = file['size'] as String?;
      if (createdTime != null) {
        try {
          final dt = DateTime.parse(createdTime).toLocal(); // 한국 시간대로 변환
          lastInfo = '마지막 백업: ${dt.year}-${dt.month.toString().padLeft(2, '0')}-${dt.day.toString().padLeft(2, '0')} '
                    '${dt.hour.toString().padLeft(2, '0')}:${dt.minute.toString().padLeft(2, '0')}';
        } catch (_) {}
      }
      if (size != null) {
        sizeInfo = ' • 크기: ${_formatFileSize(int.tryParse(size) ?? 0)}';
      }
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.backup, color: Colors.green),
                const SizedBox(width: 8),
                const Text('클라우드 백업', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 12),
            Text('$lastInfo$sizeInfo', style: const TextStyle(fontSize: 13, color: Colors.black54)),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _backupBtnLoading ? null : _backupData,
                    icon: _backupBtnLoading
                        ? const SizedBox(height: 16, width: 16, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
                        : const Icon(Icons.cloud_upload),
                    label: Text(_backupBtnLoading ? '백업 중...' : '백업 덮어쓰기'),
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: (fileId == null || _restoreBtnLoading) ? null : () => _restoreData(fileId!, 'bara_backup.json'),
                    icon: _restoreBtnLoading
                        ? const SizedBox(height: 16, width: 16, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
                        : const Icon(Icons.restore),
                    label: Text(_restoreBtnLoading ? '복원 중...' : '최신 백업 복원'),
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.orange, foregroundColor: Colors.white),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 인증 섹션 스켈레톤 (실제 UI와 동일한 구조)
  Widget _buildAuthSectionSkeleton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SkeletonLoading.text(width: 120, height: 16), // "구글 계정을 연동하여..." 텍스트
        const SizedBox(height: 8),
        SkeletonLoading.text(width: 200, height: 14), // 설명 텍스트
        const SizedBox(height: 12),
        // 버튼 영역 스켈레톤
        SkeletonLoading.custom(
          child: Container(
            width: double.infinity,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  // --- 구 UI 완전 제거 ---
  /// 파일 크기 포맷팅
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  /// 플러스 플랜 업그레이드 다이얼로그
  void _showUpgradeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('플러스 플랜 전용 기능'),
        content: const Text(
          '클라우드 백업은 플러스 플랜 전용 기능입니다.\n'
          '플러스 플랜으로 업그레이드하시겠습니까?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 구독 관리 페이지로 이동
              Navigator.of(context).pushNamed('/subscription');
            },
            child: const Text('업그레이드'),
          ),
        ],
      ),
    );
  }

  /// 데이터 복원 확인 다이얼로그
  Future<bool> _showRestoreConfirmDialog(String fileName) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('데이터 복원'),
        content: Text(
          '백업 파일 "$fileName"에서 데이터를 복원하시겠습니까?\n\n'
          '⚠️ 현재 데이터가 백업 데이터로 덮어씌워집니다.\n'
          '복원 후에는 앱을 재시작해야 합니다.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('복원하기'),
          ),
        ],
      ),
    );
    return result ?? false;
  }


  /// 구글 계정 연동 해제 확인 다이얼로그
  Future<bool> _showSignOutConfirmDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('구글 계정 연동 해제'),
        content: const Text(
          '구글 계정 연동을 해제하시겠습니까?\n\n'
          '연동을 해제하면 백업 기능을 사용할 수 없습니다.\n'
          '기존 백업 파일은 클라우드에 그대로 남아있습니다.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('연동 해제'),
          ),
        ],
      ),
    );
    return result ?? false;
  }
}
